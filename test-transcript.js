const https = require('https');
const http = require('http');

// Test video ID from your URL
const TEST_VIDEO_ID = 'aX1ht08-m_c';

function makeRequest(url) {
    return new Promise((resolve, reject) => {
        const protocol = url.startsWith('https:') ? https : http;
        
        protocol.get(url, (res) => {
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => resolve(data));
        }).on('error', reject);
    });
}

async function testTranscriptExtraction(videoId) {
    console.log(`Testing transcript extraction for video: ${videoId}`);
    console.log(`URL: https://www.youtube.com/watch?v=${videoId}`);
    
    try {
        // Method 1: Try YouTube's timedtext API directly
        console.log('\n=== Method 1: YouTube timedtext API ===');
        await testTimedTextAPI(videoId);
        
        // Method 2: Parse video page
        console.log('\n=== Method 2: Parse video page ===');
        await testVideoPageParsing(videoId);
        
    } catch (error) {
        console.error('Test failed:', error.message);
    }
}

async function testTimedTextAPI(videoId) {
    const apiUrls = [
        `https://www.youtube.com/api/timedtext?lang=en&v=${videoId}&fmt=json3`,
        `https://www.youtube.com/api/timedtext?lang=en&v=${videoId}&fmt=srv3`,
        `https://www.youtube.com/api/timedtext?lang=en&v=${videoId}`,
        `https://www.youtube.com/api/timedtext?v=${videoId}&lang=en&fmt=json3`,
    ];
    
    for (let i = 0; i < apiUrls.length; i++) {
        try {
            console.log(`Trying API URL ${i + 1}: ${apiUrls[i]}`);
            const response = await makeRequest(apiUrls[i]);
            
            if (response.includes('timedtext') || response.includes('transcript') || response.includes('text')) {
                console.log(`✅ API URL ${i + 1} returned data (${response.length} chars)`);
                console.log('First 200 chars:', response.substring(0, 200));
                
                // Try to parse as JSON
                try {
                    const data = JSON.parse(response);
                    if (data.events) {
                        console.log(`Found ${data.events.length} transcript events`);
                        const transcript = data.events
                            .filter(event => event.segs)
                            .map(event => event.segs.map(seg => seg.utf8).join(''))
                            .join(' ')
                            .trim();
                        
                        if (transcript.length > 50) {
                            console.log('✅ SUCCESS! Extracted transcript:');
                            console.log(transcript.substring(0, 300) + '...');
                            return transcript;
                        }
                    }
                } catch (e) {
                    console.log('Response is not JSON, trying XML parsing...');
                    // Try XML parsing
                    const xmlMatches = [...response.matchAll(/<text[^>]*>(.*?)<\/text>/gs)];
                    if (xmlMatches.length > 0) {
                        const transcript = xmlMatches
                            .map(match => match[1])
                            .join(' ')
                            .replace(/&#39;/g, "'")
                            .replace(/&quot;/g, '"')
                            .replace(/&amp;/g, '&')
                            .trim();
                        
                        if (transcript.length > 50) {
                            console.log('✅ SUCCESS! Extracted transcript from XML:');
                            console.log(transcript.substring(0, 300) + '...');
                            return transcript;
                        }
                    }
                }
            } else {
                console.log(`❌ API URL ${i + 1} returned no useful data`);
            }
        } catch (error) {
            console.log(`❌ API URL ${i + 1} failed:`, error.message);
        }
    }
}

async function testVideoPageParsing(videoId) {
    try {
        console.log('Fetching video page...');
        const pageHtml = await makeRequest(`https://www.youtube.com/watch?v=${videoId}`);
        console.log(`Page loaded: ${pageHtml.length} characters`);
        
        // Look for various patterns
        const patterns = [
            /"captionTracks":\[(.*?)\]/,
            /"playerCaptionsTracklistRenderer":\{"captionTracks":\[(.*?)\]/,
            /"captions":\{"playerCaptionsTracklistRenderer":\{"captionTracks":\[(.*?)\]/
        ];
        
        for (let i = 0; i < patterns.length; i++) {
            console.log(`Trying pattern ${i + 1}...`);
            const match = pageHtml.match(patterns[i]);
            if (match) {
                console.log(`✅ Pattern ${i + 1} found caption data`);
                try {
                    const captionData = `[${match[1]}]`;
                    console.log('Caption data preview:', captionData.substring(0, 200));
                    
                    const tracks = JSON.parse(captionData);
                    console.log(`Found ${tracks.length} caption tracks`);
                    
                    for (const track of tracks) {
                        console.log('Track:', {
                            languageCode: track.languageCode,
                            vssId: track.vssId,
                            name: track.name?.simpleText
                        });
                        
                        if (track.languageCode === 'en' || track.vssId?.includes('.en')) {
                            console.log('Found English track, fetching transcript...');
                            const transcriptXml = await makeRequest(track.baseUrl);
                            console.log(`Transcript XML length: ${transcriptXml.length}`);
                            
                            const textMatches = [...transcriptXml.matchAll(/<text[^>]*>(.*?)<\/text>/gs)];
                            if (textMatches.length > 0) {
                                const transcript = textMatches
                                    .map(match => match[1])
                                    .join(' ')
                                    .replace(/&#39;/g, "'")
                                    .replace(/&quot;/g, '"')
                                    .replace(/&amp;/g, '&')
                                    .trim();
                                
                                console.log('✅ SUCCESS! Extracted transcript:');
                                console.log(transcript.substring(0, 300) + '...');
                                return transcript;
                            }
                        }
                    }
                } catch (e) {
                    console.log(`❌ Failed to parse caption data:`, e.message);
                }
            } else {
                console.log(`❌ Pattern ${i + 1} not found`);
            }
        }
        
    } catch (error) {
        console.log('❌ Video page parsing failed:', error.message);
    }
}

// Run the test
testTranscriptExtraction(TEST_VIDEO_ID);
