console.log('Testing with redirect handling...');

const https = require('https');
const http = require('http');

function makeRequest(url, maxRedirects = 5) {
    return new Promise((resolve, reject) => {
        console.log(`Making request to: ${url}`);
        
        const options = {
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'identity',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1'
            }
        };
        
        const protocol = url.startsWith('https:') ? https : http;
        
        protocol.get(url, options, (res) => {
            console.log(`Response status: ${res.statusCode}`);
            console.log(`Response headers:`, res.headers);
            
            // Handle redirects
            if (res.statusCode >= 300 && res.statusCode < 400 && res.headers.location) {
                if (maxRedirects > 0) {
                    console.log(`Following redirect to: ${res.headers.location}`);
                    return makeRequest(res.headers.location, maxRedirects - 1)
                        .then(resolve)
                        .catch(reject);
                } else {
                    reject(new Error('Too many redirects'));
                    return;
                }
            }
            
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => {
                console.log(`Response length: ${data.length} characters`);
                resolve(data);
            });
        }).on('error', (err) => {
            console.error(`Request failed: ${err.message}`);
            reject(err);
        });
    });
}

async function testWithRedirects() {
    const videoId = 'aX1ht08-m_c';
    
    try {
        console.log('\n=== Fetching YouTube page with redirect handling ===');
        const pageHtml = await makeRequest(`https://www.youtube.com/watch?v=${videoId}`);
        
        console.log('\n=== Analyzing page content ===');
        
        // Check if we got the actual video page
        if (pageHtml.includes('ytInitialData') || pageHtml.includes('ytInitialPlayerResponse')) {
            console.log('✅ Got YouTube video page!');
            
            // Look for caption tracks
            if (pageHtml.includes('captionTracks')) {
                console.log('✅ Found captionTracks in page');
                
                // Try to extract caption tracks
                const captionMatch = pageHtml.match(/"captionTracks":\[(.*?)\]/);
                if (captionMatch) {
                    console.log('✅ Extracted caption tracks data');
                    console.log('Caption data preview:', captionMatch[1].substring(0, 300));
                    
                    try {
                        const tracks = JSON.parse(`[${captionMatch[1]}]`);
                        console.log(`Found ${tracks.length} tracks`);
                        
                        for (const track of tracks) {
                            console.log('Track:', {
                                lang: track.languageCode,
                                vssId: track.vssId,
                                name: track.name?.simpleText,
                                hasUrl: !!track.baseUrl
                            });
                            
                            if ((track.languageCode === 'en' || track.vssId?.includes('.en')) && track.baseUrl) {
                                console.log('✅ Found English track with URL, fetching...');
                                
                                try {
                                    const transcriptXml = await makeRequest(track.baseUrl);
                                    console.log(`Transcript XML length: ${transcriptXml.length}`);
                                    
                                    if (transcriptXml.includes('<text')) {
                                        const textMatches = [...transcriptXml.matchAll(/<text[^>]*>(.*?)<\/text>/gs)];
                                        console.log(`Found ${textMatches.length} text segments`);
                                        
                                        if (textMatches.length > 0) {
                                            const transcript = textMatches
                                                .map(match => match[1])
                                                .join(' ')
                                                .replace(/&#39;/g, "'")
                                                .replace(/&quot;/g, '"')
                                                .replace(/&amp;/g, '&')
                                                .trim();
                                            
                                            console.log('✅ SUCCESS! Transcript extracted:');
                                            console.log('Length:', transcript.length);
                                            console.log('First 500 chars:', transcript.substring(0, 500));
                                            return transcript;
                                        }
                                    } else {
                                        console.log('❌ No <text> elements found');
                                        console.log('XML preview:', transcriptXml.substring(0, 200));
                                    }
                                } catch (e) {
                                    console.log('❌ Failed to fetch transcript XML:', e.message);
                                }
                            }
                        }
                    } catch (e) {
                        console.log('❌ Failed to parse tracks:', e.message);
                    }
                }
            } else {
                console.log('❌ No captionTracks found');
                
                // Check what we did get
                console.log('Page contains:');
                console.log('- ytInitialData:', pageHtml.includes('ytInitialData'));
                console.log('- ytInitialPlayerResponse:', pageHtml.includes('ytInitialPlayerResponse'));
                console.log('- captions:', pageHtml.includes('captions'));
                console.log('- playerCaptionsTracklistRenderer:', pageHtml.includes('playerCaptionsTracklistRenderer'));
            }
        } else {
            console.log('❌ Did not get YouTube video page');
            console.log('Page preview:', pageHtml.substring(0, 500));
        }
        
    } catch (error) {
        console.error('Test failed:', error.message);
    }
}

testWithRedirects();
