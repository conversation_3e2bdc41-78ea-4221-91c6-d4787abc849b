@echo off
title YouTube Transcript Extension Launcher

echo 🚀 Starting YouTube Transcript Extension...

REM Check if we're in the right directory
if not exist "yt-transcript-mcp" (
    echo ❌ Error: yt-transcript-mcp directory not found
    echo Please run this script from the firefox-yt-plugin directory
    pause
    exit /b 1
)

if not exist "youtube-summarizer-plugin" (
    echo ❌ Error: youtube-summarizer-plugin directory not found
    echo Please run this script from the firefox-yt-plugin directory
    pause
    exit /b 1
)

REM Start MCP HTTP wrapper in background
echo 📡 Starting MCP server...
cd yt-transcript-mcp
start /B "" npm run http
cd ..

REM Wait for MCP server to start
echo ⏳ Waiting for MCP server to initialize...
timeout /t 3 /nobreak > nul

REM Check if MCP server is running
curl -s http://127.0.0.1:3001/health > nul 2>&1
if errorlevel 1 (
    echo ❌ Failed to start MCP server
    pause
    exit /b 1
)

echo ✅ MCP server running on http://127.0.0.1:3001

REM Start Firefox extension
echo 🦊 Starting Firefox extension...
cd youtube-summarizer-plugin

where web-ext > nul 2>&1
if errorlevel 1 (
    echo ❌ web-ext not found. Please install it:
    echo npm install --global web-ext
    echo.
    echo Or load the extension manually:
    echo 1. Open Firefox
    echo 2. Go to about:debugging
    echo 3. Click 'This Firefox'
    echo 4. Click 'Load Temporary Add-on'
    echo 5. Select manifest.json from: %cd%
    echo.
    echo Press any key to stop the MCP server when done...
    pause > nul
) else (
    echo Using web-ext to launch Firefox...
    web-ext run
)

REM Cleanup
echo 🔄 Shutting down...
taskkill /F /IM node.exe /FI "WINDOWTITLE eq MCP*" > nul 2>&1
echo ✅ Cleanup complete
pause