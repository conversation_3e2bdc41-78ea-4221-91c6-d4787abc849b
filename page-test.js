console.log('Testing video page parsing...');

const https = require('https');

function makeRequest(url) {
    return new Promise((resolve, reject) => {
        console.log(`Making request to: ${url}`);
        
        const options = {
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
        };
        
        https.get(url, options, (res) => {
            console.log(`Response status: ${res.statusCode}`);
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => {
                console.log(`Response length: ${data.length} characters`);
                resolve(data);
            });
        }).on('error', (err) => {
            console.error(`Request failed: ${err.message}`);
            reject(err);
        });
    });
}

async function testPageParsing() {
    const videoId = 'aX1ht08-m_c';
    
    try {
        console.log('\n=== Fetching YouTube page ===');
        const pageHtml = await makeRequest(`https://www.youtube.com/watch?v=${videoId}`);
        
        // Search for caption-related patterns
        console.log('\n=== Searching for caption patterns ===');
        
        // Pattern 1: Look for "captionTracks"
        if (pageHtml.includes('captionTracks')) {
            console.log('✅ Found "captionTracks" in page');
            
            const patterns = [
                /"captionTracks":\[(.*?)\]/,
                /"captionTracks":\[(.*?)\],"audioTracks"/,
                /"captionTracks":\[(.*?)\],"defaultAudioTrackIndex"/
            ];
            
            for (let i = 0; i < patterns.length; i++) {
                const match = pageHtml.match(patterns[i]);
                if (match) {
                    console.log(`✅ Pattern ${i + 1} matched!`);
                    console.log('Match preview:', match[1].substring(0, 200));
                    
                    try {
                        const captionData = `[${match[1]}]`;
                        const tracks = JSON.parse(captionData);
                        console.log(`Found ${tracks.length} caption tracks`);
                        
                        for (const track of tracks) {
                            console.log('Track info:', {
                                languageCode: track.languageCode,
                                vssId: track.vssId,
                                name: track.name?.simpleText || track.name?.runs?.[0]?.text,
                                baseUrl: track.baseUrl ? 'present' : 'missing'
                            });
                            
                            // Try English tracks
                            if (track.languageCode === 'en' || track.vssId?.includes('.en') || track.languageCode?.startsWith('en-')) {
                                console.log('✅ Found English track, testing URL...');
                                
                                if (track.baseUrl) {
                                    try {
                                        const transcriptXml = await makeRequest(track.baseUrl);
                                        console.log(`Transcript XML length: ${transcriptXml.length}`);
                                        
                                        if (transcriptXml.includes('<text')) {
                                            console.log('✅ Found transcript XML!');
                                            const textMatches = [...transcriptXml.matchAll(/<text[^>]*>(.*?)<\/text>/gs)];
                                            console.log(`Found ${textMatches.length} text segments`);
                                            
                                            if (textMatches.length > 0) {
                                                const transcript = textMatches
                                                    .map(match => match[1])
                                                    .join(' ')
                                                    .replace(/&#39;/g, "'")
                                                    .replace(/&quot;/g, '"')
                                                    .replace(/&amp;/g, '&')
                                                    .replace(/&lt;/g, '<')
                                                    .replace(/&gt;/g, '>')
                                                    .trim();
                                                
                                                console.log('✅ SUCCESS! Transcript extracted:');
                                                console.log('Length:', transcript.length);
                                                console.log('Preview:', transcript.substring(0, 500));
                                                return transcript;
                                            }
                                        } else {
                                            console.log('❌ No <text> tags in response');
                                            console.log('Response preview:', transcriptXml.substring(0, 200));
                                        }
                                    } catch (e) {
                                        console.log('❌ Failed to fetch transcript:', e.message);
                                    }
                                }
                            }
                        }
                    } catch (e) {
                        console.log('❌ Failed to parse caption data:', e.message);
                    }
                    break;
                }
            }
        } else {
            console.log('❌ No "captionTracks" found in page');
        }
        
        // Pattern 2: Look for other caption indicators
        console.log('\n=== Checking for other caption indicators ===');
        const indicators = ['captions', 'transcript', 'subtitles', 'timedtext'];
        for (const indicator of indicators) {
            if (pageHtml.includes(indicator)) {
                console.log(`✅ Found "${indicator}" in page`);
            }
        }
        
    } catch (error) {
        console.error('Test failed:', error.message);
    }
}

testPageParsing();
