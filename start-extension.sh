#!/bin/bash

# YouTube Transcript Extension Launcher
# This script starts both the MCP server and Firefox extension together

echo "🚀 Starting YouTube Transcript Extension..."

# Check if we're in the right directory
if [ ! -d "yt-transcript-mcp" ] || [ ! -d "youtube-summarizer-plugin" ]; then
    echo "❌ Error: Please run this script from the firefox-yt-plugin directory"
    echo "Current directory: $(pwd)"
    exit 1
fi

# Function to cleanup background processes
cleanup() {
    echo ""
    echo "🔄 Shutting down..."
    
    # Kill MCP server
    if [ ! -z "$MCP_PID" ]; then
        echo "Stopping MCP server (PID: $MCP_PID)..."
        kill $MCP_PID 2>/dev/null
    fi
    
    # Kill any remaining node processes for http-wrapper
    pkill -f "http-wrapper.js" 2>/dev/null
    
    echo "✅ Cleanup complete"
    exit 0
}

# Set up signal handlers for graceful shutdown
trap cleanup SIGINT SIGTERM

# Start MCP HTTP wrapper in background
echo "📡 Starting MCP server..."
cd yt-transcript-mcp
npm run http &
MCP_PID=$!
cd ..

# Wait for MCP server to start
echo "⏳ Waiting for MCP server to initialize..."
sleep 3

# Check if MCP server is running
if ! curl -s http://127.0.0.1:3001/health > /dev/null 2>&1; then
    echo "❌ Failed to start MCP server"
    cleanup
fi

echo "✅ MCP server running on http://127.0.0.1:3001"

# Start Firefox extension
echo "🦊 Starting Firefox extension..."
cd youtube-summarizer-plugin

if command -v web-ext > /dev/null 2>&1; then
    echo "Using web-ext to launch Firefox..."
    web-ext run
else
    echo "❌ web-ext not found. Please install it:"
    echo "npm install --global web-ext"
    echo ""
    echo "Or load the extension manually:"
    echo "1. Open Firefox"
    echo "2. Go to about:debugging"
    echo "3. Click 'This Firefox'"
    echo "4. Click 'Load Temporary Add-on'"
    echo "5. Select manifest.json from: $(pwd)"
    echo ""
    echo "Press Ctrl+C to stop the MCP server when done"
    
    # Keep the script running to maintain MCP server
    while true; do
        if ! kill -0 $MCP_PID 2>/dev/null; then
            echo "❌ MCP server stopped unexpectedly"
            break
        fi
        sleep 5
    done
fi

# Cleanup when web-ext exits
cleanup