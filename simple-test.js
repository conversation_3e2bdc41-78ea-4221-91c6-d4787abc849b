console.log('Starting transcript test...');

const https = require('https');

function makeRequest(url) {
    return new Promise((resolve, reject) => {
        console.log(`Making request to: ${url}`);
        
        https.get(url, (res) => {
            console.log(`Response status: ${res.statusCode}`);
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => {
                console.log(`Response length: ${data.length} characters`);
                resolve(data);
            });
        }).on('error', (err) => {
            console.error(`Request failed: ${err.message}`);
            reject(err);
        });
    });
}

async function testSimple() {
    const videoId = 'aX1ht08-m_c';
    
    try {
        // Test 1: Try the timedtext API
        console.log('\n=== Test 1: Timedtext API ===');
        const apiUrl = `https://www.youtube.com/api/timedtext?lang=en&v=${videoId}&fmt=json3`;
        const apiResponse = await makeRequest(apiUrl);
        
        if (apiResponse.length > 100) {
            console.log('✅ API returned data!');
            console.log('First 200 chars:', apiResponse.substring(0, 200));
            
            // Try to parse as JSON
            try {
                const data = JSON.parse(apiResponse);
                console.log('✅ Valid JSON response');
                if (data.events) {
                    console.log(`Found ${data.events.length} events`);
                    // Extract text
                    const transcript = data.events
                        .filter(event => event.segs)
                        .map(event => event.segs.map(seg => seg.utf8).join(''))
                        .join(' ')
                        .trim();
                    
                    if (transcript.length > 50) {
                        console.log('✅ SUCCESS! Transcript extracted:');
                        console.log(transcript.substring(0, 500));
                        return;
                    }
                }
            } catch (e) {
                console.log('Not JSON, checking for XML...');
                if (apiResponse.includes('<text')) {
                    console.log('✅ Found XML format');
                    const matches = [...apiResponse.matchAll(/<text[^>]*>(.*?)<\/text>/g)];
                    if (matches.length > 0) {
                        const transcript = matches.map(m => m[1]).join(' ').trim();
                        console.log('✅ SUCCESS! XML transcript extracted:');
                        console.log(transcript.substring(0, 500));
                        return;
                    }
                }
            }
        } else {
            console.log('❌ API returned minimal data');
        }
        
        // Test 2: Try different API format
        console.log('\n=== Test 2: Different API format ===');
        const apiUrl2 = `https://www.youtube.com/api/timedtext?v=${videoId}&lang=en`;
        const apiResponse2 = await makeRequest(apiUrl2);
        
        if (apiResponse2.length > 100) {
            console.log('✅ API 2 returned data!');
            console.log('First 200 chars:', apiResponse2.substring(0, 200));
            
            if (apiResponse2.includes('<text')) {
                const matches = [...apiResponse2.matchAll(/<text[^>]*>(.*?)<\/text>/g)];
                if (matches.length > 0) {
                    const transcript = matches.map(m => m[1]).join(' ').trim();
                    console.log('✅ SUCCESS! API 2 transcript extracted:');
                    console.log(transcript.substring(0, 500));
                    return;
                }
            }
        }
        
        console.log('❌ All API tests failed');
        
    } catch (error) {
        console.error('Test failed:', error.message);
    }
}

testSimple();
