# Apify YouTube Transcript Extraction Guide

## Overview

We use the Apify actor `pintostudio~youtube-transcript-scraper` to extract YouTube video transcripts. This actor provides reliable transcript extraction with proper timing data.

## API Details

- **Actor ID**: `faVsWy9VTSNVIhWpR` 
- **Full Actor Name**: `pintostudio~youtube-transcript-scraper`
- **Base URL**: `https://api.apify.com/v2/acts/pintostudio~youtube-transcript-scraper/runs`
- **API Token**: `**********************************************`

## How It Works

### 1. Start a Run

```bash
curl "https://api.apify.com/v2/acts/pintostudio~youtube-transcript-scraper/runs?token=**********************************************" \
  -X POST \
  -d '{"videoUrl": "https://www.youtube.com/watch?v=IELMSD2kdmk"}' \
  -H 'Content-Type: application/json'
```

**Response**: Returns a run object with `id` and `status: "READY"`

### 2. Check Run Status

```bash
curl "https://api.apify.com/v2/acts/pintostudio~youtube-transcript-scraper/runs/{RUN_ID}?token=**********************************************"
```

**Response**: Returns run details with `status: "SUCCEEDED"` when complete, plus `defaultDatasetId`

### 3. Get Transcript Data

```bash
curl "https://api.apify.com/v2/datasets/{DATASET_ID}/items?token=**********************************************"
```

## Response Format

The transcript data comes as an array with a single object containing a `data` array:

```json
[{
  "data": [
    {
      "start": "0.320",
      "dur": "2.160", 
      "text": "Apache spark an open- Source data"
    },
    {
      "start": "2.480",
      "dur": "1.920",
      "text": "analytics engine that can process"
    }
    // ... more transcript segments
  ]
}]
```

### Data Structure
- `start`: Start time in seconds (as string)
- `dur`: Duration in seconds (as string) 
- `text`: Transcript text for this segment

## Implementation Notes

1. **Timing**: Runs typically complete in 6-10 seconds
2. **Cost**: ~$0.007 per result (very affordable)
3. **Reliability**: Handles most YouTube videos with available captions
4. **Format**: Provides structured timing data, not just raw text

## JavaScript/Firefox Implementation

For the Firefox extension, we'll need to:

1. Use `fetch()` to make POST request to start run
2. Poll the run status endpoint until `status === "SUCCEEDED"`
3. Fetch the dataset items to get transcript data
4. Parse the response and extract the transcript text + timing

## Error Handling

- Check for `status === "SUCCEEDED"` before fetching results
- Handle cases where no transcript is available
- Implement retry logic for network failures
- Validate that dataset contains transcript data