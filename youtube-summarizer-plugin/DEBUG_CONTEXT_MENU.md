# YouTube Context Menu Debug Guide

This guide helps debug the context menu issues with YouTube video thumbnails.

## Problem Description

The "Add YouTube Video to Summarizer" context menu item is not consistently appearing when right-clicking on YouTube video thumbnails that are `<video>` HTML elements.

## Enhanced Solution Features

### 1. Comprehensive Logging
- Added detailed logging throughout the background script and content script
- All logs are prefixed with timestamps for easy tracking
- Logs include all available context information from Firefox's context menu API

### 2. Multiple Context Menu Items
- **summarizer-on-page**: For right-clicking on YouTube watch pages
- **summarizer-on-link**: For right-clicking on links/images pointing to YouTube videos
- **summarizer-on-video**: For right-clicking on `<video>` elements
- **summarizer-on-all**: Primary solution for all elements (catches video thumbnails)
- **summarizer-on-frame**: For iframe-based video elements

### 3. Multi-Layer Video ID Extraction
- **Layer 1**: Standard URL extraction from pageUrl/linkUrl/srcUrl
- **Layer 2**: DOM-based extraction via content script
- **Layer 3**: Click context analysis with enhanced selectors
- **Layer 4**: Right-click detection and video ID caching
- **Layer 5**: Fallback to last detected/hovered video

### 4. Advanced DOM Analysis
The content script now includes sophisticated DOM analysis:
- Searches for `data-video-id` and `data-context-item-id` attributes
- Looks for nearby links containing video URLs
- Checks parent elements up the DOM tree (up to 10 levels)
- Analyzes JavaScript content and `window.ytInitialData`
- Enhanced YouTube-specific selectors for current layout

### 5. Context Menu Injector (NEW)
- **Active monitoring**: Detects right-clicks on video elements in real-time
- **Video ID caching**: Stores recently detected video IDs for context menu use
- **Hover tracking**: Tracks which videos user has recently hovered over
- **Fallback detection**: Provides video IDs even when Firefox context menu fails

## Debugging Steps

### 1. Install the Enhanced Extension
1. Load the updated extension in Firefox
2. Navigate to YouTube
3. Open Firefox Developer Tools (F12)
4. Go to the Console tab

### 2. Test Context Menu Behavior
1. Right-click on various YouTube elements:
   - Video thumbnails (the problematic ones)
   - Text links to videos
   - Images/thumbnails
   - Blank areas on watch pages

2. Check the console for detailed logs showing:
   - Which context menu items are created
   - What information Firefox provides when context menus are clicked
   - The extraction process and results
   - Context menu injector activity

### 3. Use the Console Test Script
1. On any YouTube page, open the browser console
2. Copy and paste the content of `test/console-test.js`
3. Press Enter to run the comprehensive test suite
4. The script will analyze extension functionality and video detection

### 4. Use the Debug Script
1. On any YouTube page, open the browser console
2. Copy and paste the content of `debug/debug-context-menu.js`
3. Press Enter to run the script
4. The script will analyze all video elements and their context
5. Right-click on video elements to see additional debug information

### 4. Analyze the Logs

Look for these key log messages:

#### Extension Installation
```
[YT-Summarizer] Extension installed/updated, setting up context menus
[YT-Summarizer] Created context menu: summarizer-on-page
[YT-Summarizer] Created context menu: summarizer-on-link
[YT-Summarizer] Created context menu: summarizer-on-video
[YT-Summarizer] Created context menu: summarizer-on-all
```

#### Context Menu Clicks
```
[YT-Summarizer] Context menu clicked {menuItemId, pageUrl, linkUrl, srcUrl, ...}
```

#### Context Menu Injector Activity
```
[YT-Summarizer-Injector] Context menu event detected {target: "VIDEO", className: "..."}
[YT-Summarizer-Injector] Right-click on potential video element {videoId: "abc123", ...}
[YT-Summarizer-Injector] Stored video ID for potential use abc123
```

#### Video ID Extraction Process
```
[YT-Summarizer] Using page URL for video ID extraction
[YT-Summarizer] Attempt 1 - Page URL extraction
[YT-Summarizer] Attempt 2 - Source URL extraction
[YT-Summarizer] Attempt 3 - DOM extraction successful
[YT-Summarizer] Using last detected video ID from injector abc123
```

### 5. Common Issues and Solutions

#### Issue: Context menu doesn't appear on video thumbnails
**Possible causes:**
1. Firefox doesn't recognize the element as a video
2. The `documentUrlPatterns` doesn't match
3. Firefox context menu API limitations

**Debug steps:**
1. Check if the "summarizer-on-all" menu appears
2. Use the debug script to analyze the video element structure
3. Check console for context menu creation logs

#### Issue: Context menu appears but video ID extraction fails
**Possible causes:**
1. Non-standard video URLs (blob URLs, etc.)
2. Missing DOM context information
3. YouTube layout changes

**Debug steps:**
1. Check the extraction attempt logs
2. Verify what URLs are available in the context info
3. Use the debug script to see what video-related data is in the DOM

## Expected Behavior After Fix

1. **Context menu should appear** on all video thumbnails via either:
   - The "video" context menu (preferred)
   - The "all" context fallback menu

2. **Video ID extraction should succeed** via:
   - Direct URL extraction (if available)
   - DOM-based extraction from data attributes
   - DOM-based extraction from nearby links

3. **Comprehensive logging** should show exactly what's happening at each step

## Reporting Issues

When reporting issues, please include:
1. Firefox version
2. YouTube page URL where the issue occurs
3. Console logs from the extension (with timestamps)
4. Output from the debug script
5. Description of the specific video thumbnail that's not working

## Next Steps

If this enhanced solution doesn't resolve the issue, the logs will provide detailed information about:
- Whether Firefox is triggering the context menu events
- What information Firefox provides in the context
- Whether the DOM extraction methods are finding video IDs
- Any errors in the extraction process

This comprehensive approach should either fix the issue or provide enough debugging information to identify the root cause and develop a more targeted solution.
