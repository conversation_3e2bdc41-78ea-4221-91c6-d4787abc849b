#!/usr/bin/env node

/**
 * Native messaging host for Firefox extension to manage MCP server
 * This runs outside the browser sandbox and can spawn Node.js processes
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

let mcpProcess = null;
let mcpPort = 3001;

// Logging function
function log(message, data = '') {
    const timestamp = new Date().toISOString();
    console.error(`[MCP-Host ${timestamp}] ${message}`, data);
}

// Native messaging communication functions
function sendMessage(message) {
    const messageStr = JSON.stringify(message);
    const length = Buffer.byteLength(messageStr, 'utf8');
    
    // Write length as 4-byte little-endian integer
    const lengthBuffer = Buffer.allocUnsafe(4);
    lengthBuffer.writeUInt32LE(length, 0);
    
    process.stdout.write(lengthBuffer);
    process.stdout.write(messageStr, 'utf8');
}

function readMessage() {
    return new Promise((resolve, reject) => {
        // Read 4-byte length header
        const lengthBuffer = Buffer.allocUnsafe(4);
        process.stdin.read(lengthBuffer);
        
        if (lengthBuffer.length !== 4) {
            reject(new Error('Failed to read message length'));
            return;
        }
        
        const messageLength = lengthBuffer.readUInt32LE(0);
        
        // Read message content
        const messageBuffer = Buffer.allocUnsafe(messageLength);
        process.stdin.read(messageBuffer);
        
        if (messageBuffer.length !== messageLength) {
            reject(new Error('Failed to read complete message'));
            return;
        }
        
        try {
            const message = JSON.parse(messageBuffer.toString('utf8'));
            resolve(message);
        } catch (e) {
            reject(new Error('Failed to parse message JSON'));
        }
    });
}

// MCP server management functions
function startMCPServer() {
    if (mcpProcess) {
        log('MCP server already running');
        return Promise.resolve();
    }

    return new Promise((resolve, reject) => {
        log('Starting MCP HTTP wrapper...');
        
        const mcpDir = path.join(__dirname, '../../../yt-transcript-mcp');
        const mcpScript = path.join(mcpDir, 'src/http-wrapper.js');
        
        if (!fs.existsSync(mcpScript)) {
            reject(new Error(`MCP script not found: ${mcpScript}`));
            return;
        }
        
        mcpProcess = spawn('node', [mcpScript], {
            cwd: mcpDir,
            stdio: ['pipe', 'pipe', 'pipe']
        });
        
        mcpProcess.stdout.on('data', (data) => {
            log('MCP stdout:', data.toString().trim());
        });
        
        mcpProcess.stderr.on('data', (data) => {
            const output = data.toString().trim();
            log('MCP stderr:', output);
            
            // Check if server started successfully
            if (output.includes('running on http://127.0.0.1:3001')) {
                log('MCP server started successfully');
                resolve();
            }
        });
        
        mcpProcess.on('close', (code) => {
            log('MCP server closed with code:', code);
            mcpProcess = null;
        });
        
        mcpProcess.on('error', (error) => {
            log('MCP server error:', error.message);
            mcpProcess = null;
            reject(error);
        });
        
        // Timeout after 10 seconds if server doesn't start
        setTimeout(() => {
            if (mcpProcess && !mcpProcess.killed) {
                reject(new Error('MCP server startup timeout'));
            }
        }, 10000);
    });
}

function stopMCPServer() {
    if (mcpProcess) {
        log('Stopping MCP server...');
        mcpProcess.kill('SIGTERM');
        mcpProcess = null;
    }
}

function checkMCPHealth() {
    return new Promise((resolve) => {
        if (!mcpProcess) {
            resolve(false);
            return;
        }
        
        // Simple HTTP health check
        const http = require('http');
        const req = http.get('http://127.0.0.1:3001/health', (res) => {
            resolve(res.statusCode === 200);
        });
        
        req.on('error', () => resolve(false));
        req.setTimeout(2000, () => {
            req.destroy();
            resolve(false);
        });
    });
}

// Message handling
async function handleMessage(message) {
    log('Received message:', message);
    
    try {
        switch (message.action) {
            case 'start':
                await startMCPServer();
                sendMessage({ success: true, action: 'start', message: 'MCP server started' });
                break;
                
            case 'stop':
                stopMCPServer();
                sendMessage({ success: true, action: 'stop', message: 'MCP server stopped' });
                break;
                
            case 'health':
                const isHealthy = await checkMCPHealth();
                sendMessage({ success: true, action: 'health', healthy: isHealthy });
                break;
                
            case 'restart':
                stopMCPServer();
                await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second
                await startMCPServer();
                sendMessage({ success: true, action: 'restart', message: 'MCP server restarted' });
                break;
                
            default:
                sendMessage({ success: false, error: 'Unknown action: ' + message.action });
        }
    } catch (error) {
        log('Error handling message:', error.message);
        sendMessage({ success: false, error: error.message });
    }
}

// Main execution
async function main() {
    log('Native messaging host started');
    
    // Automatically start MCP server on launch
    try {
        await startMCPServer();
        sendMessage({ success: true, action: 'auto-start', message: 'MCP server auto-started' });
    } catch (error) {
        log('Failed to auto-start MCP server:', error.message);
        sendMessage({ success: false, action: 'auto-start', error: error.message });
    }
    
    // Handle incoming messages
    process.stdin.on('readable', async () => {
        try {
            const message = await readMessage();
            await handleMessage(message);
        } catch (error) {
            log('Error reading message:', error.message);
        }
    });
    
    process.stdin.on('end', () => {
        log('Extension disconnected, shutting down...');
        stopMCPServer();
        process.exit(0);
    });
}

// Graceful shutdown
process.on('SIGINT', () => {
    log('Received SIGINT, shutting down...');
    stopMCPServer();
    process.exit(0);
});

process.on('SIGTERM', () => {
    log('Received SIGTERM, shutting down...');
    stopMCPServer();
    process.exit(0);
});

// Start the host
main().catch((error) => {
    log('Fatal error:', error.message);
    process.exit(1);
});