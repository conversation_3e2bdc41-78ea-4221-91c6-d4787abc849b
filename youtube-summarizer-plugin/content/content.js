// Simple content script for YouTube video title extraction and video ID detection

// Function to extract video title from the page
function getVideoTitle() {
    // Try multiple selectors for different YouTube layouts
    const titleSelectors = [
        'h1.title.style-scope.ytd-video-primary-info-renderer',
        'h1.ytd-video-primary-info-renderer',
        'h1[class*="title"]',
        '.title.style-scope.ytd-video-primary-info-renderer',
        'meta[property="og:title"]'
    ];

    for (const selector of titleSelectors) {
        const element = document.querySelector(selector);
        if (element) {
            const title = element.textContent || element.getAttribute('content');
            if (title && title.trim()) {
                console.log(`[YT-Bookmarker] Found title using selector: ${selector}`, title.trim());
                return title.trim();
            }
        }
    }

    // Fallback: try to get from document title
    if (document.title && document.title !== 'YouTube') {
        const title = document.title.replace(' - YouTube', '');
        if (title.trim()) {
            console.log(`[YT-Bookmarker] Using document title:`, title.trim());
            return title.trim();
        }
    }

    console.log(`[YT-Bookmarker] Could not find video title`);
    return "Title not found";
}

// Function to extract video ID from various DOM elements
function getVideoIdFromDOM() {
    // Try to get video ID from URL first
    const url = window.location.href;
    
    // Handle Shorts URLs
    const shortsMatch = url.match(/youtube\.com\/shorts\/([^"&?\/\s]{11})/);
    if (shortsMatch && shortsMatch[1]) {
        console.log(`[YT-Bookmarker] Found Shorts video ID from URL:`, shortsMatch[1]);
        return shortsMatch[1];
    }
    
    // Handle regular video URLs
    const urlMatch = url.match(/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/);
    if (urlMatch && urlMatch[1]) {
        console.log(`[YT-Bookmarker] Found video ID from URL:`, urlMatch[1]);
        return urlMatch[1];
    }

    // Try to get from various DOM attributes
    const videoIdSelectors = [
        '[data-video-id]',
        '[data-context-item-id]',
        'meta[itemprop="videoId"]',
        'meta[property="og:url"]'
    ];

    for (const selector of videoIdSelectors) {
        const element = document.querySelector(selector);
        if (element) {
            let videoId = element.getAttribute('data-video-id') || 
                         element.getAttribute('data-context-item-id') ||
                         element.getAttribute('content');
            
            if (videoId) {
                // Extract video ID from URL if needed
                const match = videoId.match(/([^"&?\/\s]{11})/);
                if (match && match[1]) {
                    console.log(`[YT-Bookmarker] Found video ID from DOM:`, match[1]);
                    return match[1];
                }
            }
        }
    }

    console.log(`[YT-Bookmarker] Could not find video ID from DOM`);
    return null;
}

// Function to handle click context and extract video ID
function getVideoIdFromClickContext(clickInfo) {
    console.log(`[YT-Bookmarker] Processing click context:`, clickInfo);

    // First try the provided URLs
    const urls = [clickInfo.linkUrl, clickInfo.srcUrl, clickInfo.pageUrl].filter(Boolean);
    
    for (const url of urls) {
        // Check for Shorts first
        const shortsMatch = url.match(/youtube\.com\/shorts\/([^"&?\/\s]{11})/);
        if (shortsMatch && shortsMatch[1]) {
            console.log(`[YT-Bookmarker] Found Shorts video ID from click context URL:`, shortsMatch[1]);
            return shortsMatch[1];
        }
        
        // Then regular videos
        const match = url.match(/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/);
        if (match && match[1]) {
            console.log(`[YT-Bookmarker] Found video ID from click context URL:`, match[1]);
            return match[1];
        }
    }

    // Fallback to DOM extraction
    return getVideoIdFromDOM();
}

// Function to get title for a specific video ID by looking at links
function getVideoTitleForId(videoId) {
    if (!videoId) return "Title not found";
    
    // First check if we're on the video's page
    const currentUrl = window.location.href;
    if (currentUrl.includes(`/watch?v=${videoId}`)) {
        return getVideoTitle();
    }
    
    // Look for any element that references this video ID
    const videoIdSelectors = [
        `[href*="/watch?v=${videoId}"]`,
        `[href*="/shorts/${videoId}"]`,
        `[data-video-id="${videoId}"]`,
        `[data-context-item-id="${videoId}"]`
    ];
    
    for (const selector of videoIdSelectors) {
        const elements = document.querySelectorAll(selector);
        
        for (const element of elements) {
            // Find the container that holds both the link and the title
            const containers = [
                element.closest('ytd-rich-item-renderer'),
                element.closest('ytd-video-renderer'), 
                element.closest('ytd-compact-video-renderer'),
                element.closest('ytd-grid-video-renderer'),
                element.closest('ytd-playlist-video-renderer'),
                element.closest('ytd-reel-item-renderer'), // YouTube Shorts
                element.closest('ytd-shorts-lockup-view-model'), // YouTube Shorts
                element.closest('#dismissible'),
                element.closest('.style-scope.ytd-item-section-renderer')
            ].filter(Boolean);
            
            for (const container of containers) {
                // Look for title in various places within the container
                const titleSelectors = [
                    '#video-title',
                    '#video-title-text',
                    'h3 #video-title',
                    'h3 a#video-title-link',
                    'a[id="video-title"]',
                    'span[id="video-title"]',
                    '.title',
                    'h3.ytd-video-renderer',
                    'yt-formatted-string[class*="video-title"]'
                ];
                
                for (const titleSelector of titleSelectors) {
                    const titleElement = container.querySelector(titleSelector);
                    if (titleElement) {
                        // Get text content, handling both regular elements and yt-formatted-string
                        const titleText = (titleElement.textContent || titleElement.innerText || '').trim();
                        if (titleText && titleText.length > 0) {
                            console.log(`[YT-Bookmarker] Found title in container:`, titleText);
                            return titleText;
                        }
                    }
                }
                
                // Also check aria-label on links within container (but only video links)
                const videoLinks = container.querySelectorAll(`a[href*="/watch?v=${videoId}"], a[href*="/shorts/${videoId}"]`);
                for (const link of videoLinks) {
                    const ariaLabel = link.getAttribute('aria-label');
                    if (ariaLabel && ariaLabel.trim() && !ariaLabel.includes('SHIFT+')) {
                        console.log(`[YT-Bookmarker] Found title in aria-label:`, ariaLabel);
                        return ariaLabel.trim();
                    }
                }
            }
            
            // Direct checks on the element itself
            if (element.title && element.title.trim()) {
                console.log(`[YT-Bookmarker] Found title in title attribute:`, element.title);
                return element.title.trim();
            }
            
            const ariaLabel = element.getAttribute('aria-label');
            if (ariaLabel && ariaLabel.trim()) {
                console.log(`[YT-Bookmarker] Found title in element aria-label:`, ariaLabel);
                return ariaLabel.trim();
            }
        }
    }
    
    console.log(`[YT-Bookmarker] Could not find title for video ID:`, videoId);
    return "Title not found";
}

// Listen for messages from background script
browser.runtime.onMessage.addListener((request, sender, sendResponse) => {
    console.log(`[YT-Bookmarker] Received message:`, request);

    if (request.action === "getVideoTitle") {
        const title = getVideoTitle();
        sendResponse(title);
        return true;
    }
    
    if (request.action === "getVideoTitleForId") {
        const title = getVideoTitleForId(request.videoId);
        sendResponse(title);
        return true;
    }

    if (request.action === "getVideoIdFromClickContext") {
        const videoId = getVideoIdFromClickContext(request.clickInfo);
        sendResponse(videoId);
        return true;
    }

    return false;
});

console.log(`[YT-Bookmarker] Content script loaded on:`, window.location.href);