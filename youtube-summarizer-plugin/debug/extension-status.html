<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Extension Status Check</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .status-item {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .status-ok {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .status-error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .status-warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        #results {
            margin-top: 20px;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>YouTube Summarizer Extension Status</h1>
    
    <div class="status-item status-warning">
        <strong>Instructions:</strong> This page helps diagnose extension issues. Click the buttons below to run various tests.
    </div>
    
    <button onclick="checkExtensionBasics()">Check Extension Basics</button>
    <button onclick="checkContextMenus()">Check Context Menus</button>
    <button onclick="checkPermissions()">Check Permissions</button>
    <button onclick="checkStorage()">Check Storage</button>
    <button onclick="runAllTests()">Run All Tests</button>
    <button onclick="clearResults()">Clear Results</button>
    
    <div id="results"></div>
    
    <script>
        function log(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `status-item status-${type === 'error' ? 'error' : type === 'warning' ? 'warning' : 'ok'}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            results.appendChild(div);
            console.log(message);
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        async function checkExtensionBasics() {
            log("=== Checking Extension Basics ===");
            
            try {
                // Check if browser API is available
                if (typeof browser === 'undefined') {
                    log("❌ Browser API not available", 'error');
                    return false;
                }
                log("✅ Browser API is available");
                
                // Check if we can access runtime
                if (browser.runtime && browser.runtime.id) {
                    log(`✅ Extension ID: ${browser.runtime.id}`);
                } else {
                    log("❌ Cannot access extension runtime", 'error');
                    return false;
                }
                
                // Check manifest
                const manifest = browser.runtime.getManifest();
                log(`✅ Manifest version: ${manifest.manifest_version}`);
                log(`✅ Extension name: ${manifest.name}`);
                log(`✅ Extension version: ${manifest.version}`);
                
                return true;
            } catch (error) {
                log(`❌ Error checking extension basics: ${error.message}`, 'error');
                return false;
            }
        }
        
        async function checkContextMenus() {
            log("=== Checking Context Menus ===");
            
            try {
                if (!browser.contextMenus) {
                    log("❌ Context menus API not available", 'error');
                    return false;
                }
                log("✅ Context menus API is available");
                
                // Try to create a test context menu
                try {
                    browser.contextMenus.create({
                        id: "test-menu",
                        title: "Test Menu",
                        contexts: ["all"]
                    }, () => {
                        if (browser.runtime.lastError) {
                            log(`❌ Error creating test context menu: ${browser.runtime.lastError.message}`, 'error');
                        } else {
                            log("✅ Successfully created test context menu");
                            // Clean up
                            browser.contextMenus.remove("test-menu");
                        }
                    });
                } catch (error) {
                    log(`❌ Exception creating test context menu: ${error.message}`, 'error');
                }
                
                return true;
            } catch (error) {
                log(`❌ Error checking context menus: ${error.message}`, 'error');
                return false;
            }
        }
        
        async function checkPermissions() {
            log("=== Checking Permissions ===");
            
            try {
                const manifest = browser.runtime.getManifest();
                
                log(`✅ Declared permissions: ${manifest.permissions.join(', ')}`);
                log(`✅ Host permissions: ${manifest.host_permissions.join(', ')}`);
                
                // Check if we can access YouTube
                try {
                    const tabs = await browser.tabs.query({url: "*://*.youtube.com/*"});
                    log(`✅ Found ${tabs.length} YouTube tabs`);
                } catch (error) {
                    log(`⚠️ Cannot query YouTube tabs: ${error.message}`, 'warning');
                }
                
                return true;
            } catch (error) {
                log(`❌ Error checking permissions: ${error.message}`, 'error');
                return false;
            }
        }
        
        async function checkStorage() {
            log("=== Checking Storage ===");
            
            try {
                if (!browser.storage || !browser.storage.local) {
                    log("❌ Storage API not available", 'error');
                    return false;
                }
                log("✅ Storage API is available");
                
                // Try to read storage
                const result = await browser.storage.local.get('videos');
                const videoCount = result.videos ? result.videos.length : 0;
                log(`✅ Storage accessible, ${videoCount} videos stored`);
                
                // Try to write to storage
                await browser.storage.local.set({test: 'test-value'});
                const testResult = await browser.storage.local.get('test');
                if (testResult.test === 'test-value') {
                    log("✅ Storage write/read test successful");
                    await browser.storage.local.remove('test');
                } else {
                    log("❌ Storage write/read test failed", 'error');
                }
                
                return true;
            } catch (error) {
                log(`❌ Error checking storage: ${error.message}`, 'error');
                return false;
            }
        }
        
        async function runAllTests() {
            clearResults();
            log("🚀 Running comprehensive extension tests...");
            
            const results = {
                basics: await checkExtensionBasics(),
                contextMenus: await checkContextMenus(),
                permissions: await checkPermissions(),
                storage: await checkStorage()
            };
            
            log("=== Test Summary ===");
            const passed = Object.values(results).filter(Boolean).length;
            const total = Object.keys(results).length;
            
            if (passed === total) {
                log(`🎉 All tests passed (${passed}/${total})`, 'ok');
                log("✅ Extension appears to be working correctly");
            } else {
                log(`⚠️ Some tests failed (${passed}/${total})`, 'warning');
                log("❌ Extension may have issues that need to be resolved");
            }
            
            log("=== Next Steps ===");
            if (results.contextMenus) {
                log("✅ Context menus should be working - try right-clicking on YouTube");
            } else {
                log("❌ Context menu issues detected - check Firefox extension permissions");
            }
        }
        
        // Auto-run basic check on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                log("🔍 Auto-running basic extension check...");
                checkExtensionBasics();
            }, 500);
        });
    </script>
</body>
</html>
