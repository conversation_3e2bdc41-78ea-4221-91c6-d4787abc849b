// Enhanced logging function
function debugLog(message, data = null) {
    const timestamp = new Date().toISOString();
    console.log(`[YT-Bookmarker ${timestamp}] ${message}`, data || '');
}

// A helper function to extract the YouTube video ID from a URL
function getVideoId(url) {
    if (!url) return null;
    
    // Handle YouTube Shorts URLs first
    const shortsMatch = url.match(/youtube\.com\/shorts\/([^"&?\/\s]{11})/);
    if (shortsMatch && shortsMatch[1]) {
        debugLog("Found Shorts video ID", shortsMatch[1]);
        return shortsMatch[1];
    }
    
    // Regular YouTube video regex
    const youtubeRegex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/;
    const match = url.match(youtubeRegex);
    return (match && match[1]) ? match[1] : null;
}

// Function to create context menus with error handling
function createContextMenus() {
    debugLog("Creating context menus...");

    // Remove any existing menus to ensure a clean state
    browser.contextMenus.removeAll().then(() => {
        debugLog("Cleared existing context menus");

        // Create a single comprehensive menu that works in all contexts
        const menuConfig = {
            id: "bookmark-youtube-video",
            title: "Bookmark YouTube Video",
            contexts: ["page", "link", "image", "video", "frame"],
            documentUrlPatterns: ["*://*.youtube.com/*"]
        };

        try {
            browser.contextMenus.create(menuConfig, () => {
                if (browser.runtime.lastError) {
                    debugLog(`Error creating context menu:`, browser.runtime.lastError.message);
                } else {
                    debugLog(`Successfully created context menu: ${menuConfig.id}`);
                }
            });
        } catch (error) {
            debugLog(`Exception creating context menu:`, error.message);
        }

    }).catch(error => {
        debugLog("Error removing existing context menus:", error.message);
    });
}

// Create context menus immediately when the background script loads
debugLog("Background script loaded, creating context menus immediately");
createContextMenus();

// Fired when the extension is first installed, or updated.
browser.runtime.onInstalled.addListener(() => {
    debugLog("Extension installed/updated, setting up context menus");
    createContextMenus();
});

// Simple verification that context menus are working
setTimeout(() => {
    debugLog("Verifying context menus after delay...");
    if (browser.contextMenus) {
        debugLog("Context menu API is available");
    } else {
        debugLog("ERROR: Context menu API not available");
    }
}, 1000);

// Fired when a context menu item is clicked
// Function to extract transcript using MCP server
async function getYouTubeTranscript(videoId) {
    debugLog("Requesting transcript from MCP server for video", videoId);
    
    try {
        const response = await fetch('http://127.0.0.1:3001/transcript', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ videoId: videoId })
        });
        
        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
        }
        
        const data = await response.json();
        
        if (data.success && data.transcript) {
            debugLog("Successfully got transcript from MCP server", data.transcript.length + " characters");
            return data.transcript;
        } else {
            throw new Error("MCP server returned no transcript data");
        }
        
    } catch (error) {
        debugLog("MCP server request failed:", error.message);
        
        // Check if it's a connection error
        if (error.message.includes('fetch')) {
            throw new Error("Could not connect to MCP server. Make sure it's running on http://127.0.0.1:3001");
        }
        
        throw error;
    }
}

// Function to generate transcript HTML page
function generateTranscriptPage(transcript, videoTitle, videoId) {
    const html = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Transcript: ${videoTitle}</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9f9f9;
        }
        .header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .header h1 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 24px;
        }
        .video-link {
            color: #1976d2;
            text-decoration: none;
            font-weight: 500;
        }
        .video-link:hover {
            text-decoration: underline;
        }
        .transcript {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            font-size: 16px;
            line-height: 1.8;
            color: #444;
        }
        .transcript p {
            margin-bottom: 1em;
        }
        .meta {
            color: #666;
            font-size: 14px;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>${videoTitle}</h1>
        <div class="meta">Video ID: ${videoId}</div>
        <a href="https://www.youtube.com/watch?v=${videoId}" class="video-link" target="_blank">
            🎥 Watch on YouTube
        </a>
    </div>
    
    <div class="transcript">
        <h2>Transcript</h2>
        ${transcript.split('. ').map(sentence => 
            sentence.trim() ? `<p>${sentence.trim()}${sentence.trim().endsWith('.') ? '' : '.'}</p>` : ''
        ).join('')}
    </div>
</body>
</html>`;
    
    return 'data:text/html;charset=utf-8,' + encodeURIComponent(html);
}

// Message listener for transcript requests
browser.runtime.onMessage.addListener(async (message, sender, sendResponse) => {
    if (message.action === 'getTranscript') {
        try {
            debugLog("Received transcript request", message);
            
            const transcript = await getYouTubeTranscript(message.videoId);
            const transcriptPage = generateTranscriptPage(transcript, message.videoTitle, message.videoId);
            
            // Open transcript in new tab
            await browser.tabs.create({ url: transcriptPage });
            
            // Optionally store transcript for future use
            const result = await browser.storage.local.get('videos');
            const videos = result.videos || [];
            const videoIndex = videos.findIndex(v => v.id === message.videoId);
            
            if (videoIndex !== -1) {
                videos[videoIndex].transcript = transcript;
                videos[videoIndex].transcriptStatus = 'success';
                await browser.storage.local.set({ videos: videos });
                debugLog("Saved transcript to storage for video", message.videoId);
            }
            
        } catch (error) {
            debugLog("Error getting transcript:", error.message);
            
            // Show error page
            const errorPage = generateTranscriptPage(
                `Sorry, no transcript is available for this video. This could be because:
                
                • The video doesn't have captions enabled
                • The video has auto-generated captions that aren't accessible via API
                • The video is private or restricted
                • Technical issues with YouTube's transcript service
                
                You can try watching the video directly and enabling captions manually.`,
                message.videoTitle,
                message.videoId
            );
            await browser.tabs.create({ url: errorPage });
            
            // Update storage with error status
            const result = await browser.storage.local.get('videos');
            const videos = result.videos || [];
            const videoIndex = videos.findIndex(v => v.id === message.videoId);
            
            if (videoIndex !== -1) {
                videos[videoIndex].transcriptStatus = 'error';
                await browser.storage.local.set({ videos: videos });
            }
        }
    }
});

browser.contextMenus.onClicked.addListener(async (info, tab) => {
    try {
        debugLog("Context menu clicked", {
            menuItemId: info.menuItemId,
            pageUrl: info.pageUrl,
            linkUrl: info.linkUrl,
            srcUrl: info.srcUrl,
            tabUrl: tab.url,
            tabTitle: tab.title
        });

        // Make sure we're on YouTube
        if (!tab.url || !tab.url.includes('youtube.com')) {
            debugLog("Not on YouTube, ignoring");
            return;
        }

        let videoUrl = null;
        let videoId = null;

        // Try all possible sources to extract video ID
        debugLog("Attempting video ID extraction");

        // Try all possible URLs first
        const possibleUrls = [info.linkUrl, info.srcUrl, info.pageUrl, tab.url].filter(Boolean);
        debugLog("Trying all possible URLs", possibleUrls);

        for (const url of possibleUrls) {
            videoId = getVideoId(url);
            if (videoId) {
                videoUrl = url;
                debugLog("Found video ID from URL", { url, videoId });
                break;
            }
        }

    // If still no video ID, try DOM extraction
    if (!videoId && tab.url && tab.url.includes('youtube.com')) {
        try {
            // Make sure content script is injected first
            await browser.tabs.executeScript(tab.id, {
                file: "content/content.js",
                runAt: "document_idle"
            }).catch(e => debugLog("Content script already injected or failed to inject", e.message));
            
            const domVideoId = await browser.tabs.sendMessage(tab.id, {
                action: "getVideoIdFromClickContext",
                clickInfo: {
                    srcUrl: info.srcUrl,
                    pageUrl: info.pageUrl,
                    linkUrl: info.linkUrl,
                    menuItemId: info.menuItemId
                }
            });
            if (domVideoId) {
                videoId = domVideoId;
                videoUrl = `https://www.youtube.com/watch?v=${videoId}`;
                debugLog("DOM extraction successful", videoId);
            }
        } catch (error) {
            debugLog("DOM extraction failed", error.message);
        }
    }

    if (!videoId) {
        debugLog("ERROR: Could not determine YouTube video ID", {
            videoUrl,
            allInfo: info,
            tabInfo: { url: tab.url, title: tab.title }
        });
        return;
    }

    debugLog("Successfully extracted video ID, proceeding to save", videoId);

    try {
        const currentVideos = (await browser.storage.local.get('videos')).videos || [];

        if (currentVideos.some(video => video.id === videoId)) {
            debugLog(`Video ${videoId} is already bookmarked, skipping`);
            return;
        }

        let title = `YouTube Video - ${videoId}`; // Default placeholder title

        // Try to fetch the title from tab title if we're on a watch page
        if (tab.url && tab.url.includes('/watch?v=' + videoId)) {
            // We're on the actual video page, use the tab title
            if (tab.title && tab.title !== 'YouTube') {
                title = tab.title.replace(' - YouTube', '').trim();
                debugLog("Using tab title for video", title);
            }
        } else {
            // We're not on the video page, try to get title from content script
            debugLog("Attempting to fetch video title from content script");
            try {
                // Make sure content script is loaded
                await browser.tabs.executeScript(tab.id, {
                    file: "content/content.js", 
                    runAt: "document_idle"
                }).catch(() => {}); // Ignore if already injected
                
                const fetchedTitle = await browser.tabs.sendMessage(tab.id, { 
                    action: "getVideoTitleForId",
                    videoId: videoId 
                });
                if (fetchedTitle && fetchedTitle !== "Title not found") {
                    title = fetchedTitle;
                    debugLog("Successfully fetched title from content script", title);
                } else {
                    debugLog("Content script returned no valid title, using placeholder");
                }
            } catch (error) {
                debugLog("Could not fetch title from content script", error.message);
            }
        }

        const newVideo = {
            id: videoId,
            url: `https://www.youtube.com/watch?v=${videoId}`,
            title: title,
            thumbnailUrl: `https://i.ytimg.com/vi/${videoId}/mqdefault.jpg`,
            dateAdded: Date.now(),
            transcript: null,
            transcriptStatus: 'pending'
        };

        const updatedVideos = [newVideo, ...currentVideos];
        await browser.storage.local.set({ videos: updatedVideos });
        debugLog("Successfully bookmarked video", newVideo);

    } catch (error) {
        debugLog("ERROR: Failed to save video", error);
    }
    } catch (error) {
        debugLog("ERROR: Unhandled error in context menu handler", error);
        console.error(error);
    }
});