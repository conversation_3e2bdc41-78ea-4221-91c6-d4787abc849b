document.addEventListener('DOMContentLoaded', () => {
    console.log("Panel DOM content loaded.");
    const videoListContainer = document.getElementById('video-list-container');

    function render(videos) {
        console.log("Rendering videos:", videos);
        videoListContainer.innerHTML = '';
        
        if (!videos || videos.length === 0) {
            videoListContainer.innerHTML = '<p class="empty-message">No videos bookmarked yet. Right-click on YouTube videos to bookmark them!</p>';
            return;
        }
        
        videos.forEach(video => {
            const videoItem = document.createElement('div');
            videoItem.className = 'video-item';

            videoItem.innerHTML = `
                <img src="${video.thumbnailUrl}" class="thumbnail clickable" alt="Video thumbnail" data-url="${video.url}">
                <div class="info">
                    <p class="title clickable" data-url="${video.url}">${video.title}</p>
                    <p class="date">Added: ${new Date(video.dateAdded).toLocaleDateString()}</p>
                </div>
                <div class="actions">
                    <button class="transcript-btn" data-id="${video.id}" data-title="${video.title}">Transcript</button>
                    <button class="delete-btn" data-id="${video.id}">Delete</button>
                </div>
            `;

            videoListContainer.appendChild(videoItem);
        });

        // Add event listeners for clickable elements (thumbnails and titles)
        document.querySelectorAll('.clickable').forEach(element => {
            element.addEventListener('click', (e) => {
                const url = e.target.dataset.url;
                browser.tabs.create({ url: url });
                window.close(); // Close the popup
            });
        });

        // Add event listeners for transcript buttons
        document.querySelectorAll('.transcript-btn').forEach(btn => {
            btn.addEventListener('click', async (e) => {
                const videoId = e.target.dataset.id;
                const videoTitle = e.target.dataset.title;
                
                // Send message to background script to handle transcript
                try {
                    await browser.runtime.sendMessage({
                        action: 'getTranscript',
                        videoId: videoId,
                        videoTitle: videoTitle
                    });
                    window.close(); // Close the popup
                } catch (error) {
                    console.error('Error requesting transcript:', error);
                }
            });
        });

        document.querySelectorAll('.delete-btn').forEach(btn => {
            btn.addEventListener('click', async (e) => {
                const videoId = e.target.dataset.id;
                await deleteVideo(videoId);
            });
        });
    }

    async function deleteVideo(videoId) {
        try {
            const result = await browser.storage.local.get('videos');
            const videos = result.videos || [];
            const updatedVideos = videos.filter(video => video.id !== videoId);
            await browser.storage.local.set({ videos: updatedVideos });
            console.log(`Deleted video ${videoId}`);
        } catch (error) {
            console.error("Error deleting video:", error);
        }
    }

    function loadAndRenderVideos() {
        console.log("Loading and rendering videos from storage.");
        browser.storage.local.get('videos').then(result => {
            console.log("Loaded videos from storage:", result.videos);
            const videos = result.videos || [];
            render(videos);
        }, error => {
            console.error("Error loading videos from storage:", error);
        });
    }

    browser.storage.onChanged.addListener((changes, area) => {
        console.log("Storage changed in area:", area);
        if (area === 'local' && changes.videos) {
            console.log("'videos' in storage changed. New value:", changes.videos.newValue);
            render(changes.videos.newValue || []);
        }
    });

    loadAndRenderVideos();
});