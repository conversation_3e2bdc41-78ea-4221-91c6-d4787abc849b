document.addEventListener("DOMContentLoaded", () => {
  console.log("Panel DOM content loaded.");
  const videoListContainer = document.getElementById("video-list-container");

  function render(videos) {
    console.log("Rendering videos:", videos);
    videoListContainer.innerHTML = "";

    if (!videos || videos.length === 0) {
      videoListContainer.innerHTML =
        '<p class="empty-message">No videos bookmarked yet. Right-click on YouTube videos to bookmark them!</p>';
      return;
    }

    videos.forEach((video) => {
      const videoItem = document.createElement("div");
      videoItem.className = "video-item";

      videoItem.innerHTML = `
                <img src="${
                  video.thumbnailUrl
                }" class="thumbnail clickable" alt="Video thumbnail" data-url="${
        video.url
      }">
                <div class="info">
                    <p class="title clickable" data-url="${video.url}">${
        video.title
      }</p>
                    <p class="date">Added: ${new Date(
                      video.dateAdded,
                    ).toLocaleDateString()}</p>
                </div>
                <div class="actions">
                    <button class="transcript-btn" data-id="${
                      video.id
                    }" data-title="${video.title}">Transcript</button>
                    <button class="delete-btn" data-id="${
                      video.id
                    }">Delete</button>
                </div>
            `;

      videoListContainer.appendChild(videoItem);
    });

    // Add event listeners for clickable elements (thumbnails and titles)
    document.querySelectorAll(".clickable").forEach((element) => {
      element.addEventListener("click", (e) => {
        const url = e.target.dataset.url;
        browser.tabs.create({ url: url });
        window.close(); // Close the popup
      });
    });

    // Add event listeners for transcript buttons
    console.log(
      "[Panel] Setting up transcript button listeners, found buttons:",
      document.querySelectorAll(".transcript-btn").length,
    );
    document.querySelectorAll(".transcript-btn").forEach((btn) => {
      btn.addEventListener("click", async (e) => {
        console.log("[Panel] Transcript button clicked!", e.target);
        const videoId = e.target.dataset.id;
        const videoTitle = e.target.dataset.title;

        console.log("[Panel] Video details:", { videoId, videoTitle });

        // Send message to background script to handle transcript
        try {
          console.log("[Panel] Sending message to background script...");
          const response = await browser.runtime.sendMessage({
            action: "getTranscript",
            videoId: videoId,
            videoTitle: videoTitle,
          });
          console.log("[Panel] Background script response:", response);
          window.close(); // Close the popup
        } catch (error) {
          console.error("[Panel] Error requesting transcript:", error);
        }
      });
    });

    document.querySelectorAll(".delete-btn").forEach((btn) => {
      btn.addEventListener("click", async (e) => {
        const videoId = e.target.dataset.id;
        await deleteVideo(videoId);
      });
    });
  }

  async function deleteVideo(videoId) {
    try {
      const result = await browser.storage.local.get("videos");
      const videos = result.videos || [];
      const updatedVideos = videos.filter((video) => video.id !== videoId);
      await browser.storage.local.set({ videos: updatedVideos });
      console.log(`Deleted video ${videoId}`);
    } catch (error) {
      console.error("Error deleting video:", error);
    }
  }

  function loadAndRenderVideos() {
    console.log("Loading and rendering videos from storage.");
    browser.storage.local.get("videos").then(
      (result) => {
        console.log("Loaded videos from storage:", result.videos);
        const videos = result.videos || [];
        render(videos);
      },
      (error) => {
        console.error("Error loading videos from storage:", error);
      },
    );
  }

  browser.storage.onChanged.addListener((changes, area) => {
    console.log("Storage changed in area:", area);
    if (area === "local" && changes.videos) {
      console.log(
        "'videos' in storage changed. New value:",
        changes.videos.newValue,
      );
      render(changes.videos.newValue || []);
    }
  });

  loadAndRenderVideos();
});

// Initialize when DOM is ready
document.addEventListener("DOMContentLoaded", async () => {
  console.log("[Panel] DOMContentLoaded event fired, initializing panel...");

  // Debug: Check what's in the DOM
  setTimeout(() => {
    console.log("[Panel] DOM check after 1 second:");
    console.log("[Panel] Total elements:", document.querySelectorAll("*").length);
    console.log("[Panel] Transcript buttons found:", document.querySelectorAll(".transcript-btn").length);
    console.log("[Panel] All buttons:", document.querySelectorAll("button"));
    console.log("[Panel] Elements with transcript-btn class:", document.querySelectorAll(".transcript-btn"));

    // Try to find any elements that might be transcript buttons
    const allElements = document.querySelectorAll("*");
    allElements.forEach(el => {
      if (el.textContent && el.textContent.toLowerCase().includes("transcript")) {
        console.log("[Panel] Found element with 'transcript' text:", el);
      }
    });
  }, 1000);
});
