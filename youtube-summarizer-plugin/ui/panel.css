body {
    width: 400px;
    min-height: 200px;
    margin: 0;
    padding: 0;
    font-family: Arial, sans-serif;
    background-color: #f9f9f9;
}

.header {
    background-color: #fff;
    padding: 15px;
    border-bottom: 1px solid #ddd;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.header h2 {
    margin: 0;
    color: #333;
    font-size: 18px;
    text-align: center;
}

#video-list-container {
    padding: 10px;
    max-height: 500px;
    overflow-y: auto;
}

.empty-message {
    text-align: center;
    color: #888;
    font-style: italic;
    margin: 30px 10px;
    line-height: 1.4;
}

.video-item {
    display: flex;
    align-items: center;
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 5px;
    margin-bottom: 10px;
    padding: 10px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.thumbnail {
    width: 80px;
    height: 60px;
    object-fit: cover;
    border-radius: 3px;
    margin-right: 10px;
    flex-shrink: 0;
}

.thumbnail.clickable {
    cursor: pointer;
    transition: opacity 0.2s;
}

.thumbnail.clickable:hover {
    opacity: 0.8;
}

.info {
    flex: 1;
    min-width: 0;
}

.title {
    margin: 0 0 5px 0;
    font-weight: bold;
    font-size: 14px;
    line-height: 1.3;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.title.clickable {
    cursor: pointer;
    color: #1976d2;
    transition: color 0.2s;
}

.title.clickable:hover {
    color: #1565c0;
    text-decoration: underline;
}

.date {
    margin: 0;
    font-size: 12px;
    color: #666;
}

.actions {
    display: flex;
    flex-direction: column;
    gap: 5px;
    margin-left: 10px;
}

.transcript-btn, .delete-btn {
    padding: 6px 12px;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    font-size: 12px;
    font-weight: bold;
    transition: background-color 0.2s;
}

.transcript-btn {
    background-color: #1976d2;
    color: white;
}

.transcript-btn:hover {
    background-color: #1565c0;
}

.delete-btn {
    background-color: #666;
    color: white;
}

.delete-btn:hover {
    background-color: #444;
}