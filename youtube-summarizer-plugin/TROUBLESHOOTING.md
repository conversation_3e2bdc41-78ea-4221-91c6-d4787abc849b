# YouTube Summarizer Extension Troubleshooting

## 🚨 Critical Issue Identified

Based on the console logs, the **context menu injector is working perfectly** (detecting video IDs), but the **Firefox context menu items are not appearing**. This indicates a fundamental issue with context menu registration.

## 🔧 Immediate Fixes Applied

### 1. **Manifest V3 Compatibility Fix**
- **Issue**: Background script was using old `"scripts"` format
- **Fix**: Changed to `"service_worker"` format for Manifest V3
- **Before**: `"scripts": ["background/background.js"]`
- **After**: `"service_worker": "background/background.js"`

### 2. **Enhanced Context Menu Creation**
- Added comprehensive error handling for context menu creation
- Added multiple creation attempts (onInstalled, onStartup, immediate)
- Added verification and debugging functions

## 🔍 Diagnostic Steps

### Step 1: Reload the Extension
1. Go to `about:debugging` in Firefox
2. Find "YouTube Video Summarizer" extension
3. Click "Reload" to apply the manifest fix
4. Check console for new debug messages

### Step 2: Verify Extension Status
1. Open the debug status page: `debug/extension-status.html`
2. Click "Run All Tests" to verify extension functionality
3. Look for any permission or API issues

### Step 3: Check Context Menu Creation
Look for these specific log messages in the Firefox console:
```
[YT-Summarizer] Background script loaded, creating context menus immediately
[YT-Summarizer] Successfully created context menu: summarizer-on-page
[YT-Summarizer] Successfully created context menu: summarizer-on-link
[YT-Summarizer] Successfully created context menu: summarizer-on-video
[YT-Summarizer] Successfully created context menu: summarizer-on-all
```

### Step 4: Test Context Menu Appearance
1. Navigate to YouTube
2. Right-click on various elements
3. Look for "Add YouTube Video to Summarizer" in the context menu

## 🐛 Common Issues and Solutions

### Issue 1: Context Menus Not Appearing
**Symptoms**: No extension menu items in right-click context menu
**Causes**:
- Manifest V3 service worker not loading properly
- Context menu API permissions issue
- Extension not properly reloaded after changes

**Solutions**:
1. **Reload Extension**: Go to `about:debugging` → Reload extension
2. **Check Permissions**: Ensure `contextMenus` permission is granted
3. **Verify Logs**: Check for context menu creation errors in console

### Issue 2: Extension Not Loading
**Symptoms**: No debug logs appearing at all
**Causes**:
- Service worker not starting
- JavaScript errors preventing execution
- Manifest syntax errors

**Solutions**:
1. **Check Extension Status**: Use `debug/extension-status.html`
2. **Review Console**: Look for JavaScript errors
3. **Validate Manifest**: Ensure JSON syntax is correct

### Issue 3: Context Menu API Errors
**Symptoms**: Error messages about context menu creation
**Possible Error Messages**:
- "Cannot create item with duplicate id"
- "Invalid context type"
- "Permission denied"

**Solutions**:
1. **Clear Existing Menus**: Extension now calls `removeAll()` before creating
2. **Check Context Types**: Verify Firefox supports all context types used
3. **Verify Permissions**: Ensure `contextMenus` permission is in manifest

## 🔧 Advanced Debugging

### Enable Verbose Logging
The extension now includes comprehensive logging. To see all logs:
1. Open Firefox Developer Tools (F12)
2. Go to Console tab
3. Right-click on YouTube elements
4. Look for `[YT-Summarizer]` and `[YT-Summarizer-Injector]` messages

### Test Context Menu API Directly
Run this in the Firefox console to test context menu API:
```javascript
// Test if context menu API is available
console.log("Context menu API available:", !!browser.contextMenus);

// Try to create a simple test menu
browser.contextMenus.create({
    id: "test-menu",
    title: "Test Menu",
    contexts: ["all"]
}, () => {
    if (browser.runtime.lastError) {
        console.error("Context menu error:", browser.runtime.lastError.message);
    } else {
        console.log("Test context menu created successfully");
    }
});
```

### Check Service Worker Status
In Firefox, go to `about:debugging` → This Firefox → Extensions → YouTube Video Summarizer → Inspect
This opens the service worker console where you can see background script logs.

## 📋 Verification Checklist

After applying fixes, verify:

- [ ] Extension reloaded successfully
- [ ] No JavaScript errors in console
- [ ] Context menu creation logs appear
- [ ] Context menu items appear when right-clicking on YouTube
- [ ] Video ID detection logs appear when right-clicking video thumbnails
- [ ] Extension can successfully add videos to storage

## 🎯 Expected Behavior After Fix

1. **Context Menu Appearance**: "Add YouTube Video to Summarizer" should appear in right-click menu on YouTube
2. **Video Detection**: Console should show video ID detection when right-clicking video thumbnails
3. **Storage**: Videos should be added to extension storage and appear in popup panel

## 🆘 If Issues Persist

If context menus still don't appear after these fixes:

1. **Try Firefox Safe Mode**: Test if other extensions are interfering
2. **Check Firefox Version**: Ensure Firefox supports Manifest V3 extensions
3. **Test in Clean Profile**: Create new Firefox profile to test extension
4. **Review Firefox Console**: Look for any Firefox-specific error messages

## 📞 Reporting Issues

When reporting persistent issues, include:
1. Firefox version
2. Output from `debug/extension-status.html`
3. Complete console logs from extension loading
4. Screenshots of context menu (or lack thereof)
5. Any error messages from Firefox's extension debugging tools

The enhanced debugging and manifest fixes should resolve the context menu visibility issue. The core video detection functionality is already working perfectly!
