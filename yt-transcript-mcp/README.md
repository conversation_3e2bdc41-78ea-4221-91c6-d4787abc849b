# YouTube Transcript MCP Server

An MCP (Model Context Protocol) server that provides YouTube transcript retrieval functionality.

## Features

- Extract transcripts from YouTube videos using video ID or full URL
- Support for raw text or chunked formatting
- Built on the MCP SDK for easy integration with AI assistants

## Installation

```bash
npm install
```

## Usage

Start the server:
```bash
npm start
```

## Available Tools

### get_youtube_transcript
Get the transcript for a YouTube video with formatting options.

**Parameters:**
- `video_id` (required): YouTube video ID or full YouTube URL
- `format` (optional): Output format - "raw" or "chunked" (default: "raw")
- `chunk_size` (optional): Number of words per chunk when using chunked format (default: 50)

### get_transcript_chunks
Get YouTube transcript formatted in readable chunks.

**Parameters:**
- `video_id` (required): YouTube video ID or full YouTube URL  
- `chunk_size` (optional): Number of words per chunk (default: 50)

## Dependencies

- Uses Python `youtube_transcript_api` library via the transcript functions in `../yt-transcript-lambda/`
- Requires Python 3 with `youtube_transcript_api` installed

## Testing

Test the server with a sample video:

```bash
echo '{"jsonrpc":"2.0","id":1,"method":"tools/call","parameters":{"name":"get_youtube_transcript","arguments":{"video_id":"MhQ5leomEOU","format":"chunked"}}}' | npm start
```

## Integration

This MCP server can be integrated with:
- Firefox browser extensions
- Claude Desktop
- Other MCP-compatible clients

The server communicates via JSON-RPC over stdio, making it compatible with the MCP protocol standard.