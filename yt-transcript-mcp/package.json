{"name": "yt-transcript-mcp", "version": "1.0.0", "main": "src/server.js", "type": "module", "scripts": {"start": "node src/server.js", "http": "node src/http-wrapper.js"}, "keywords": ["mcp", "youtube", "transcript"], "author": "", "license": "ISC", "description": "MCP server for YouTube transcript retrieval", "dependencies": {"@modelcontextprotocol/sdk": "^1.12.0", "express": "^4.18.2", "cors": "^2.8.5"}}