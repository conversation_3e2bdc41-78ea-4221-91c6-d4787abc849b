#!/usr/bin/env node

import { Server } from "@modelcontextprotocol/sdk/server/index.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
} from "@modelcontextprotocol/sdk/types.js";
import { spawn } from "child_process";
import { fileURLToPath } from "url";
import { dirname, join } from "path";
import { existsSync } from "fs";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

console.error("YouTube Transcript MCP Server starting...");

/**
 * Create an MCP server with YouTube transcript capabilities
 */
const server = new Server(
  {
    name: "yt-transcript-mcp",
    version: "1.0.0"
  },
  {
    capabilities: {
      tools: {}
    }
  }
);

/**
 * Execute transcript binary to get transcript
 */
async function getTranscript(videoId) {
  return new Promise((resolve, reject) => {
    const transcriptDir = join(__dirname, "../../yt-transcript-lambda");
    const transcriptBinary = join(transcriptDir, "dist/transcript_cli");
    
    // Check if compiled binary exists, otherwise fall back to Python script
    let cmd, args;
    if (existsSync(transcriptBinary)) {
      cmd = transcriptBinary;
      args = [videoId];
    } else {
      // Fallback to Python script approach
      const venvPython = join(transcriptDir, ".venv/bin/python");
      const pythonCmd = existsSync(venvPython) ? venvPython : "python3";
      cmd = pythonCmd;
      args = ["-c", `
import sys
sys.path.append('${transcriptDir}')
from transcript_functions import get_full_transcript
try:
    result = get_full_transcript('${videoId}')
    print(result)
except Exception as e:
    print(f"ERROR: {e}")
    sys.exit(1)
`];
    }
    
    const process = spawn(cmd, args);

    let stdout = "";
    let stderr = "";

    process.stdout.on("data", (data) => {
      stdout += data.toString();
    });

    process.stderr.on("data", (data) => {
      stderr += data.toString();
    });

    process.on("close", (code) => {
      if (code !== 0) {
        reject(new Error(`Transcript process failed: ${stderr}`));
      } else if (stdout.startsWith("ERROR:")) {
        reject(new Error(stdout.substring(7).trim()));
      } else {
        resolve(stdout.trim());
      }
    });

    process.on("error", (error) => {
      reject(new Error(`Failed to start transcript process: ${error.message}`));
    });
  });
}

/**
 * Format transcript into chunks for better readability
 */
function formatTranscriptChunks(transcript, chunkSize = 50) {
  const words = transcript.split(" ");
  const chunks = [];
  
  for (let i = 0; i < words.length; i += chunkSize) {
    chunks.push(words.slice(i, i + chunkSize).join(" "));
  }
  
  return chunks.map((chunk, index) => `[Chunk ${index + 1}]\n${chunk}`).join("\n\n");
}

/**
 * Extract video ID from YouTube URL
 */
function extractVideoId(input) {
  // If it's already a video ID (11 characters)
  if (/^[a-zA-Z0-9_-]{11}$/.test(input)) {
    return input;
  }
  
  // Extract from various YouTube URL formats
  const patterns = [
    /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([a-zA-Z0-9_-]{11})/,
    /youtube\.com\/watch\?.*v=([a-zA-Z0-9_-]{11})/
  ];
  
  for (const pattern of patterns) {
    const match = input.match(pattern);
    if (match) {
      return match[1];
    }
  }
  
  throw new Error("Invalid YouTube URL or video ID");
}

/**
 * Handler that lists available tools
 */
server.setRequestHandler(ListToolsRequestSchema, async () => {
  console.error("List tools requested");

  return {
    tools: [
      {
        name: "get_youtube_transcript",
        description: "Get the transcript for a YouTube video",
        inputSchema: {
          type: "object",
          properties: {
            video_id: {
              type: "string",
              description: "YouTube video ID or full YouTube URL"
            },
            format: {
              type: "string",
              description: "Output format: 'raw' or 'chunked'",
              enum: ["raw", "chunked"],
              default: "raw"
            },
            chunk_size: {
              type: "number",
              description: "Number of words per chunk (only used with 'chunked' format)",
              default: 50
            }
          },
          required: ["video_id"]
        }
      },
      {
        name: "get_transcript_chunks",
        description: "Get YouTube transcript formatted in readable chunks",
        inputSchema: {
          type: "object",
          properties: {
            video_id: {
              type: "string",
              description: "YouTube video ID or full YouTube URL"
            },
            chunk_size: {
              type: "number",
              description: "Number of words per chunk",
              default: 50
            }
          },
          required: ["video_id"]
        }
      }
    ]
  };
});

/**
 * Handler for tool execution
 */
server.setRequestHandler(CallToolRequestSchema, async (request) => {
  console.error(`Tool called: ${request.params.name}`);

  switch (request.params.name) {
    case "get_youtube_transcript": {
      const videoInput = String(request.params.arguments?.video_id || "");
      const format = String(request.params.arguments?.format || "raw");
      const chunkSize = Number(request.params.arguments?.chunk_size || 50);

      if (!videoInput) {
        throw new Error("Video ID or URL is required");
      }

      try {
        const videoId = extractVideoId(videoInput);
        console.error(`Getting transcript for video: ${videoId}`);
        
        const transcript = await getTranscript(videoId);
        
        if (!transcript) {
          return {
            content: [
              {
                type: "text",
                text: `No transcript found for video ID: ${videoId}`
              }
            ]
          };
        }

        let formattedTranscript;
        if (format === "chunked") {
          formattedTranscript = formatTranscriptChunks(transcript, chunkSize);
        } else {
          formattedTranscript = transcript;
        }

        return {
          content: [
            {
              type: "text",
              text: `Transcript for YouTube video ${videoId}:\n\n${formattedTranscript}`
            }
          ]
        };
      } catch (error) {
        console.error("Transcript error:", error.message);
        return {
          content: [
            {
              type: "text",
              text: `Error getting transcript: ${error.message}`
            }
          ],
          isError: true
        };
      }
    }

    case "get_transcript_chunks": {
      const videoInput = String(request.params.arguments?.video_id || "");
      const chunkSize = Number(request.params.arguments?.chunk_size || 50);

      if (!videoInput) {
        throw new Error("Video ID or URL is required");
      }

      try {
        const videoId = extractVideoId(videoInput);
        console.error(`Getting chunked transcript for video: ${videoId}`);
        
        const transcript = await getTranscript(videoId);
        
        if (!transcript) {
          return {
            content: [
              {
                type: "text",
                text: `No transcript found for video ID: ${videoId}`
              }
            ]
          };
        }

        const formattedTranscript = formatTranscriptChunks(transcript, chunkSize);

        return {
          content: [
            {
              type: "text",
              text: `Transcript for YouTube video ${videoId} (${chunkSize} words per chunk):\n\n${formattedTranscript}`
            }
          ]
        };
      } catch (error) {
        console.error("Transcript error:", error.message);
        return {
          content: [
            {
              type: "text",
              text: `Error getting transcript: ${error.message}`
            }
          ],
          isError: true
        };
      }
    }

    default:
      throw new Error(`Unknown tool: ${request.params.name}`);
  }
});

/**
 * Start the server using stdio transport
 */
async function main() {
  console.error("YouTube Transcript MCP Server ready");
  
  const transport = new StdioServerTransport();
  await server.connect(transport);
  console.error("MCP server connected and ready for client communication");
}

// Handle graceful shutdown
process.on("SIGINT", () => {
  console.error("\nShutting down gracefully...");
  process.exit(0);
});

process.on("SIGTERM", () => {
  console.error("\nShutting down gracefully...");
  process.exit(0);
});

// Prevent unhandled promise rejections from crashing
process.on("unhandledRejection", (reason, promise) => {
  console.error("Unhandled Rejection at:", promise, "reason:", reason);
});

// Start the server
main().catch((error) => {
  console.error("Error in server main execution:", error);
  process.exit(1);
});