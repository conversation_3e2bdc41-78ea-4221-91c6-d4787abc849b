#!/usr/bin/env node

import express from 'express';
import cors from 'cors';
import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const app = express();
const PORT = 3001;

// Enable CORS for browser extension
app.use(cors({
    origin: ['moz-extension://*', 'chrome-extension://*'],
    credentials: true
}));

app.use(express.json());

console.log('Starting HTTP wrapper for MCP server...');

/**
 * Call MCP server via WSL (like <PERSON> does)
 */
async function getTranscriptFromMCP(videoId) {
    return new Promise((resolve, reject) => {
        // Use WSL to call the MCP server, just like <PERSON> Desktop config
        const mcpProcess = spawn('wsl', [
            '-e',
            'node',
            '/mnt/c/winDev/firefox-yt-plugin/yt-transcript-mcp/src/server.js'
        ], {
            stdio: ['pipe', 'pipe', 'pipe']
        });

        let stdout = '';
        let stderr = '';

        mcpProcess.stdout.on('data', (data) => {
            stdout += data.toString();
        });

        mcpProcess.stderr.on('data', (data) => {
            stderr += data.toString();
            // Log MCP server messages
            console.error('MCP:', data.toString().trim());
        });

        mcpProcess.on('close', (code) => {
            if (code !== 0) {
                reject(new Error(`MCP process failed with code ${code}: ${stderr}`));
            } else {
                try {
                    // Parse the MCP JSON-RPC response
                    const lines = stdout.trim().split('\n');
                    const lastLine = lines[lines.length - 1];
                    const response = JSON.parse(lastLine);
                    
                    if (response.result && response.result.content && response.result.content[0]) {
                        const transcript = response.result.content[0].text;
                        // Remove the prefix added by MCP server
                        const cleanTranscript = transcript.replace(/^Transcript for YouTube video [^:]+:\s*/, '');
                        resolve(cleanTranscript);
                    } else if (response.error) {
                        reject(new Error(response.error.message || 'MCP server returned error'));
                    } else {
                        reject(new Error('No transcript found in MCP response'));
                    }
                } catch (e) {
                    reject(new Error(`Failed to parse MCP response: ${e.message}\nOutput: ${stdout}`));
                }
            }
        });

        mcpProcess.on('error', (error) => {
            reject(new Error(`Failed to start MCP process via WSL: ${error.message}`));
        });

        // Send MCP JSON-RPC request
        const mcpRequest = {
            jsonrpc: "2.0",
            id: 1,
            method: "tools/call",
            params: {
                name: "get_youtube_transcript",
                arguments: {
                    video_id: videoId,
                    format: "raw"
                }
            }
        };

        mcpProcess.stdin.write(JSON.stringify(mcpRequest) + '\n');
        mcpProcess.stdin.end();
    });
}

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({ status: 'ok', message: 'MCP HTTP wrapper is running' });
});

// Get transcript endpoint
app.post('/transcript', async (req, res) => {
    try {
        const { videoId } = req.body;
        
        if (!videoId) {
            return res.status(400).json({ error: 'Video ID is required' });
        }

        console.log(`Getting transcript for video: ${videoId}`);
        
        const transcript = await getTranscriptFromMCP(videoId);
        
        if (transcript && transcript.length > 0) {
            res.json({ 
                success: true, 
                transcript: transcript
            });
        } else {
            res.status(404).json({ error: 'No transcript found for this video' });
        }
        
    } catch (error) {
        console.error('Error getting transcript:', error.message);
        res.status(500).json({ error: error.message });
    }
});

app.listen(PORT, '127.0.0.1', () => {
    console.log(`MCP HTTP wrapper running on http://127.0.0.1:${PORT}`);
    console.log('Endpoints:');
    console.log('  GET /health - Health check');
    console.log('  POST /transcript - Get video transcript');
});