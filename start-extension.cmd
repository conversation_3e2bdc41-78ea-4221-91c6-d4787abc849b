@echo off
title YouTube Transcript Extension Launcher

echo 🚀 Starting YouTube Transcript Extension...

REM Check if we're in the right directory
if not exist "yt-transcript-mcp" (
    echo ❌ Error: yt-transcript-mcp directory not found
    echo Please run this script from the firefox-yt-plugin directory
    echo Current directory: %cd%
    pause
    exit /b 1
)

if not exist "youtube-summarizer-plugin" (
    echo ❌ Error: youtube-summarizer-plugin directory not found
    echo Please run this script from the firefox-yt-plugin directory
    echo Current directory: %cd%
    pause
    exit /b 1
)

REM Start HTTP wrapper in background
echo 📡 Starting HTTP wrapper (Windows) -^> MCP server (WSL)...
cd yt-transcript-mcp
start /B "HTTP-Wrapper" cmd /c "npm run http"
cd ..

REM Wait for HTTP wrapper to start
echo ⏳ Waiting for HTTP wrapper to initialize...
timeout /t 5 /nobreak > nul

REM Check if HTTP wrapper is running
echo 🧪 Testing HTTP wrapper...
curl -s -m 5 http://127.0.0.1:3001/health > nul 2>&1
if errorlevel 1 (
    echo ❌ Failed to start HTTP wrapper
    echo Make sure Node.js is installed and npm dependencies are available
    pause
    exit /b 1
)

echo ✅ HTTP wrapper running on http://127.0.0.1:3001

REM Test WSL-MCP connection
echo 🧪 Testing WSL-MCP connection...
echo {"videoId":"shOLHtyUaFg"} > test_request.json
curl -s -X POST -H "Content-Type: application/json" -d @test_request.json http://127.0.0.1:3001/transcript -m 30 > test_response.json 2>&1
if errorlevel 1 (
    echo ❌ WSL-MCP connection test failed
    echo Make sure WSL is working and Python dependencies are installed in WSL
    del test_request.json test_response.json 2>nul
    pause
    exit /b 1
)

findstr "success" test_response.json > nul
if errorlevel 1 (
    echo ❌ WSL-MCP test failed - no success response
    echo Response:
    type test_response.json
    del test_request.json test_response.json 2>nul
    pause
    exit /b 1
)

echo ✅ WSL-MCP connection working!
del test_request.json test_response.json 2>nul

REM Start Firefox extension
echo 🦊 Starting Firefox extension...
cd youtube-summarizer-plugin

where web-ext > nul 2>&1
if errorlevel 1 (
    echo ❌ web-ext not found. Please install it:
    echo npm install --global web-ext
    echo.
    echo Or load the extension manually:
    echo 1. Open Firefox
    echo 2. Go to about:debugging
    echo 3. Click 'This Firefox'
    echo 4. Click 'Load Temporary Add-on'
    echo 5. Select manifest.json from: %cd%
    echo.
    echo Press any key to stop the servers when done...
    pause > nul
) else (
    echo Using web-ext to launch Firefox...
    web-ext run
)

REM Cleanup
echo 🔄 Shutting down...
taskkill /F /FI "WINDOWTITLE eq HTTP-Wrapper" > nul 2>&1
taskkill /F /IM node.exe /FI "COMMANDLINE eq *http-wrapper*" > nul 2>&1
echo ✅ Cleanup complete
pause