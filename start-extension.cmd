@echo off
title YouTube Transcript Extension Launcher

echo 🚀 Starting YouTube Transcript Extension...

REM Check if we're in the right directory
if not exist "youtube-summarizer-plugin" (
    echo ❌ Error: youtube-summarizer-plugin directory not found
    echo Please run this script from the firefox-yt-plugin directory
    echo Current directory: %cd%
    pause
    exit /b 1
)

echo ✅ Using Apify actor for transcript extraction - no local servers needed!

REM Start Firefox extension
echo 🦊 Starting Firefox extension...
cd youtube-summarizer-plugin

where web-ext > nul 2>&1
if errorlevel 1 (
    echo ❌ web-ext not found. Please install it:
    echo npm install --global web-ext
    echo.
    echo Or load the extension manually:
    echo 1. Open Firefox
    echo 2. Go to about:debugging
    echo 3. Click 'This Firefox'
    echo 4. Click 'Load Temporary Add-on'
    echo 5. Select manifest.json from: %cd%
    echo.
    echo Press any key to stop the servers when done...
    pause > nul
) else (
    echo Using web-ext to launch Firefox...
    web-ext run
)

REM Cleanup
echo 🔄 Shutting down...
echo ✅ Cleanup complete
pause