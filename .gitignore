# Documentation files
how-to-build-a-firefox-extension.md

# AI Reference materials
.ai-reference/

# Development artifacts
node_modules/
*.log
.DS_Store

# Build outputs
web-ext-artifacts/
*.zip
*.xpi

# IDE files
.vscode/
.idea/
*.swp
*.swo
*.cursorrules
CLAUDE.md

# Temporary files
*.tmp
*.temp

# AWS Lambda artifacts
yt-transcript-lambda/.aws-sam/
yt-transcript-lambda/package/
yt-transcript-lambda/*.zip
yt-transcript-lambda/__pycache__/
yt-transcript-lambda/*.pyc
yt-transcript-lambda/.pytest_cache/
yt-transcript-lambda/venv/
yt-transcript-lambda/.venv/
yt-transcript-lambda/.env