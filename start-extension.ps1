# YouTube Transcript Extension Launcher for Windows
# This script runs the HTTP wrapper on Windows and connects to WSL-based MCP server

Write-Host "🚀 Starting YouTube Transcript Extension..." -ForegroundColor Green

# Check if we're in the right directory
if (-not (Test-Path "yt-transcript-mcp") -or -not (Test-Path "youtube-summarizer-plugin")) {
    Write-Host "❌ Error: Please run this script from the firefox-yt-plugin directory" -ForegroundColor Red
    Write-Host "Current directory: $(Get-Location)" -ForegroundColor Yellow
    exit 1
}

# Function to cleanup background processes
function Cleanup {
    Write-Host ""
    Write-Host "🔄 Shutting down..." -ForegroundColor Yellow
    
    # Kill HTTP wrapper processes
    Get-Process -Name "node" -ErrorAction SilentlyContinue | Where-Object { $_.CommandLine -like "*http-wrapper*" } | Stop-Process -Force
    
    Write-Host "✅ Cleanup complete" -ForegroundColor Green
}

# Set up cleanup on script exit
Register-EngineEvent PowerShell.Exiting -Action { Cleanup }

# Start HTTP wrapper in background
Write-Host "📡 Starting HTTP wrapper (Windows) -> MCP server (WSL)..." -ForegroundColor Cyan
Set-Location yt-transcript-mcp
$mcpJob = Start-Job -ScriptBlock {
    Set-Location $using:PWD
    npm run http
}
Set-Location ..

# Wait for HTTP wrapper to start
Write-Host "⏳ Waiting for HTTP wrapper to initialize..." -ForegroundColor Yellow
Start-Sleep -Seconds 5

# Check if HTTP wrapper is running
try {
    $response = Invoke-RestMethod -Uri "http://127.0.0.1:3001/health" -TimeoutSec 5
    Write-Host "✅ HTTP wrapper running on http://127.0.0.1:3001" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to start HTTP wrapper" -ForegroundColor Red
    Cleanup
    exit 1
}

# Test with a sample video to ensure WSL connection works
Write-Host "🧪 Testing WSL-MCP connection..." -ForegroundColor Cyan
try {
    $testBody = @{ videoId = "shOLHtyUaFg" } | ConvertTo-Json
    $testResponse = Invoke-RestMethod -Uri "http://127.0.0.1:3001/transcript" -Method POST -Body $testBody -ContentType "application/json" -TimeoutSec 30
    if ($testResponse.success) {
        Write-Host "✅ WSL-MCP connection working! Transcript length: $($testResponse.transcript.Length) chars" -ForegroundColor Green
    } else {
        Write-Host "❌ WSL-MCP test failed" -ForegroundColor Red
        Cleanup
        exit 1
    }
} catch {
    Write-Host "❌ WSL-MCP connection test failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Make sure WSL is working and Python dependencies are installed" -ForegroundColor Yellow
    Cleanup
    exit 1
}

# Start Firefox extension
Write-Host "🦊 Starting Firefox extension..." -ForegroundColor Cyan
Set-Location youtube-summarizer-plugin

if (Get-Command "web-ext" -ErrorAction SilentlyContinue) {
    Write-Host "Using web-ext to launch Firefox..." -ForegroundColor Green
    web-ext run
} else {
    Write-Host "❌ web-ext not found. Please install it:" -ForegroundColor Red
    Write-Host "npm install --global web-ext" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Or load the extension manually:" -ForegroundColor Yellow
    Write-Host "1. Open Firefox" -ForegroundColor White
    Write-Host "2. Go to about:debugging" -ForegroundColor White
    Write-Host "3. Click 'This Firefox'" -ForegroundColor White
    Write-Host "4. Click 'Load Temporary Add-on'" -ForegroundColor White
    Write-Host "5. Select manifest.json from: $(Get-Location)" -ForegroundColor White
    Write-Host ""
    Write-Host "Press any key to stop the servers when done..." -ForegroundColor Yellow
    Read-Host
}

# Cleanup when done
Cleanup